package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.request.ReportRequestDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.Resource;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ReportService
 * Tests Excel report generation functionality
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Report Service Tests")
class ReportServiceTest {

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @Mock
    private SubmissionFilterService filterService;

    @InjectMocks
    private ReportService reportService;

    private ReportRequestDTO sampleRequest;
    private Submission sampleSubmission;

    @BeforeEach
    void setUp() {
        sampleRequest = ReportRequestDTO.builder()
                .startDate("2024-01-01")
                .endDate("2024-01-31")
                .status("Pending")
                .build();

        sampleSubmission = Submission.builder()
                .id(1L)
                .referenceNumber("REF-001")
                .submitterName("John Doe")
                .submitterJob("HRBP")
                .status("Pending")
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary Adjustment")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .directorate("IT")
                .eligible(true)

                .remarks("Test remarks")
                .build();
    }

    @Nested
    @DisplayName("Generate Report Excel Tests")
    class GenerateReportExcelTests {

        @Test
        @DisplayName("Should generate Excel report successfully with valid data")
        void testGenerateReportExcel_Success() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 31, 23, 59, 59);
            List<Submission> submissions = Arrays.asList(sampleSubmission);

            when(filterService.parseStartDate("2024-01-01")).thenReturn(startDate);
            when(filterService.parseEndDate("2024-01-31")).thenReturn(endDate);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(submissions);

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When
                ResponseEntity<Resource> result = reportService.generateReportExcel(sampleRequest);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getStatusCode().is2xxSuccessful()).isTrue();
                assertThat(result.getBody()).isNotNull();
                assertThat(result.getHeaders().getContentDisposition().getFilename())
                        .contains("submission_report_");

                verify(filterService).parseStartDate("2024-01-01");
                verify(filterService).parseEndDate("2024-01-31");
                verify(submissionRepository).findAll(any(Specification.class));
            }
        }

        @Test
        @DisplayName("Should throw exception when no submissions found")
        void testGenerateReportExcel_NoDataFound() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 31, 23, 59, 59);

            when(filterService.parseStartDate("2024-01-01")).thenReturn(startDate);
            when(filterService.parseEndDate("2024-01-31")).thenReturn(endDate);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(Collections.emptyList());

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When & Then
                assertThatThrownBy(() -> reportService.generateReportExcel(sampleRequest))
                        .isInstanceOf(CustomBadRequestException.class)
                        .hasMessageContaining("No submissions found matching the specified criteria");
            }
        }

        @Test
        @DisplayName("Should throw exception when user not authenticated")
        void testGenerateReportExcel_UserNotAuthenticated() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 31, 23, 59, 59);

            when(filterService.parseStartDate("2024-01-01")).thenReturn(startDate);
            when(filterService.parseEndDate("2024-01-31")).thenReturn(endDate);

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.empty());

                // When & Then
                assertThatThrownBy(() -> reportService.generateReportExcel(sampleRequest))
                        .isInstanceOf(CustomBadRequestException.class)
                        .hasMessageContaining("User not authenticated");
            }
        }

        @Test
        @DisplayName("Should throw exception when start date is null")
        void testGenerateReportExcel_NullStartDate() {
            // Given
            ReportRequestDTO requestWithNullStartDate = ReportRequestDTO.builder()
                    .endDate("2024-01-31")
                    .status("Pending")
                    .build();

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When & Then
                assertThatThrownBy(() -> reportService.generateReportExcel(requestWithNullStartDate))
                        .isInstanceOf(CustomBadRequestException.class)
                        .hasMessageContaining("Start date is required");
            }
        }

        @Test
        @DisplayName("Should throw exception when end date is null")
        void testGenerateReportExcel_NullEndDate() {
            // Given
            ReportRequestDTO requestWithNullEndDate = ReportRequestDTO.builder()
                    .startDate("2024-01-01")
                    .status("Pending")
                    .build();

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When & Then
                assertThatThrownBy(() -> reportService.generateReportExcel(requestWithNullEndDate))
                        .isInstanceOf(CustomBadRequestException.class)
                        .hasMessageContaining("End date is required");
            }
        }

        @Test
        @DisplayName("Should throw exception when both dates are null")
        void testGenerateReportExcel_BothDatesNull() {
            // Given
            ReportRequestDTO requestWithNullDates = ReportRequestDTO.builder()
                    .status("Pending")
                    .build();

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When & Then
                assertThatThrownBy(() -> reportService.generateReportExcel(requestWithNullDates))
                        .isInstanceOf(CustomBadRequestException.class)
                        .hasMessageContaining("Start date is required");
            }
        }

        @Test
        @DisplayName("Should throw exception when start date is empty")
        void testGenerateReportExcel_EmptyStartDate() {
            // Given
            ReportRequestDTO requestWithEmptyStartDate = ReportRequestDTO.builder()
                    .startDate("")
                    .endDate("2024-01-31")
                    .status("Pending")
                    .build();

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When & Then
                assertThatThrownBy(() -> reportService.generateReportExcel(requestWithEmptyStartDate))
                        .isInstanceOf(CustomBadRequestException.class)
                        .hasMessageContaining("Start date is required");
            }
        }

        @Test
        @DisplayName("Should throw exception when end date is empty")
        void testGenerateReportExcel_EmptyEndDate() {
            // Given
            ReportRequestDTO requestWithEmptyEndDate = ReportRequestDTO.builder()
                    .startDate("2024-01-01")
                    .endDate("")
                    .status("Pending")
                    .build();

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When & Then
                assertThatThrownBy(() -> reportService.generateReportExcel(requestWithEmptyEndDate))
                        .isInstanceOf(CustomBadRequestException.class)
                        .hasMessageContaining("End date is required");
            }
        }

        @Test
        @DisplayName("Should throw exception when start date is after end date")
        void testGenerateReportExcel_InvalidDateRange() {
            // Given
            ReportRequestDTO requestWithInvalidRange = ReportRequestDTO.builder()
                    .startDate("2024-01-31")
                    .endDate("2024-01-01")
                    .status("Pending")
                    .build();

            LocalDateTime startDate = LocalDateTime.of(2024, 1, 31, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 1, 23, 59, 59);

            when(filterService.parseStartDate("2024-01-31")).thenReturn(startDate);
            when(filterService.parseEndDate("2024-01-01")).thenReturn(endDate);

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When & Then
                assertThatThrownBy(() -> reportService.generateReportExcel(requestWithInvalidRange))
                        .isInstanceOf(CustomBadRequestException.class)
                        .hasMessageContaining("Start date cannot be after end date");
            }
        }

        @Test
        @DisplayName("Should gather all submissions when status is not provided")
        void testGenerateReportExcel_NullStatus() {
            // Given
            ReportRequestDTO requestWithNullStatus = ReportRequestDTO.builder()
                    .startDate("2024-01-01")
                    .endDate("2024-01-31")
                    .build();

            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 31, 23, 59, 59);
            List<Submission> submissions = Arrays.asList(sampleSubmission);

            when(filterService.parseStartDate("2024-01-01")).thenReturn(startDate);
            when(filterService.parseEndDate("2024-01-31")).thenReturn(endDate);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(submissions);

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When
                ResponseEntity<Resource> result = reportService.generateReportExcel(requestWithNullStatus);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getStatusCode().is2xxSuccessful()).isTrue();
                assertThat(result.getBody()).isNotNull();

                verify(submissionRepository).findAll(any(Specification.class));
            }
        }

        @Test
        @DisplayName("Should gather all submissions when status is empty")
        void testGenerateReportExcel_EmptyStatus() {
            // Given
            ReportRequestDTO requestWithEmptyStatus = ReportRequestDTO.builder()
                    .startDate("2024-01-01")
                    .endDate("2024-01-31")
                    .status("")
                    .build();

            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 31, 23, 59, 59);
            List<Submission> submissions = Arrays.asList(sampleSubmission);

            when(filterService.parseStartDate("2024-01-01")).thenReturn(startDate);
            when(filterService.parseEndDate("2024-01-31")).thenReturn(endDate);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(submissions);

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When
                ResponseEntity<Resource> result = reportService.generateReportExcel(requestWithEmptyStatus);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getStatusCode().is2xxSuccessful()).isTrue();
                assertThat(result.getBody()).isNotNull();

                verify(submissionRepository).findAll(any(Specification.class));
            }
        }

        @Test
        @DisplayName("Should propagate filter service exceptions")
        void testGenerateReportExcel_FilterServiceException() {
            // Given
            when(filterService.parseStartDate("invalid-date"))
                    .thenThrow(new CustomBadRequestException(400, "Bad Request", "Invalid date format"));

            ReportRequestDTO invalidRequest = ReportRequestDTO.builder()
                    .startDate("invalid-date")
                    .endDate("2024-01-31")
                    .status("Pending")
                    .build();

            // When & Then
            assertThatThrownBy(() -> reportService.generateReportExcel(invalidRequest))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Invalid date format");
        }
    }

    @Nested
    @DisplayName("Excel File Generation Tests")
    class ExcelFileGenerationTests {

        @Test
        @DisplayName("Should handle submissions with null values")
        void testExcelGeneration_WithNullValues() {
            // Given
            Submission submissionWithNulls = Submission.builder()
                    .id(2L)
                    .referenceNumber("REF-002")
                    .submitterName("Jane Doe")
                    .submitterJob("HRREWARDS")
                    .status("Approved")
                    .nip("987654321")
                    .name("Jane Doe")
                    .grade("B")
                    .paymentType("Bonus Staggered")
                    .amount(new BigDecimal("3000000"))
                    .description("Performance bonus")
                    .monthOfProcess("February")
                    .yearOfProcess("2024")
                    .directorate("HR")
                    .eligible(false)
     // Null payment date
                    .remarks(null) // Null remarks
                    .build();

            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 31, 23, 59, 59);
            List<Submission> submissions = Arrays.asList(submissionWithNulls);

            when(filterService.parseStartDate("2024-01-01")).thenReturn(startDate);
            when(filterService.parseEndDate("2024-01-31")).thenReturn(endDate);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(submissions);

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                        .thenReturn(Optional.of("testuser"));

                // When
                ResponseEntity<Resource> result = reportService.generateReportExcel(sampleRequest);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getStatusCode().is2xxSuccessful()).isTrue();
                assertThat(result.getBody()).isNotNull();
            }
        }
    }
}
